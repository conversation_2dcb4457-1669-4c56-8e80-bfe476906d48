//accountpage
import { createFileRoute } from '@tanstack/react-router'
import { useSession } from '@/lib/auth-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { User, Crown, Zap, ChevronDown, Target, Check, Loader2, Calendar, CreditCard, X } from 'lucide-react'
import { LogOutButton } from '@/components/auth/logout-button'
import { useCustomer, useAutumn } from 'autumn-js/react'
import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { shouldShowTrialMessaging, getSubscriptionButtonText } from '@/lib/mobile-web-bridge'
import { useQuery } from 'convex/react'
import { api } from 'database'

export const Route = createFileRoute('/account/')({
  component: AccountPage,
})

// Mobile detection hook
function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return isMobile;
}

function AccountPage() {
  const { data: session, isPending } = useSession()
  const { customer } = useCustomer()
  const { attach } = useAutumn()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null)
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false)
  const [isPaymentHistoryOpen, setIsPaymentHistoryOpen] = useState(false)
  const [activeSection, setActiveSection] = useState('basic-info')
  const isMobile = useIsMobile()
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Get real data from Convex
  const userSubscription = useQuery(
    api.subscriptions.getUserSubscription,
    session?.user?.id ? { userId: session.user.id } : "skip"
  )
  const userOnboardingStatus = useQuery(
    api.onboarding.getUserOnboardingStatus,
    session?.user?.id ? { userId: session.user.id as any } : "skip"
  )
  const billingHistory = useQuery(
    api.billing.getUserBillingHistory,
    session?.user?.id ? { userId: session.user.id } : "skip"
  )

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Show loading state while session is being fetched
  if (isPending) {
    return (
      <div className="min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if no session
  if (!session?.user) {
    window.location.href = '/login'
    return null
  }

  const user = session.user
  const userInitials = user.name
    ? user.name.split(' ').map(n => n[0]).join('').toUpperCase()
    : user.email?.charAt(0).toUpperCase() || 'U'

  // Get subscription data from Autumn and Convex
  const currentProduct = customer?.products?.[0] // Get first active product
  const messagesFeature = customer?.features ? Object.values(customer.features).find((f: any) => f.feature_id === 'messages') : null
  const messagesUsed = messagesFeature ? ((messagesFeature as any).limit - (messagesFeature.balance || 0)) : 0
  const messagesLimit = (messagesFeature as any)?.limit || 50

  // Use real data from Convex and Autumn
  const hasActiveSubscription = userSubscription?.hasActiveSubscription || false
  const subscription = userSubscription?.subscription || null
  const trialStatus = userOnboardingStatus || { hasActivatedTrial: false }

  // Calculate trial days remaining if user has activated trial
  const getTrialDaysRemaining = () => {
    if (!trialStatus.hasActivatedTrial) return 7
    return 7 // Mock for now
  }

  const trialDaysRemaining = getTrialDaysRemaining()

  // Plan configurations with USD pricing
  const planConfigs = {
    'foundation_plan': {
      name: 'Foundation Plan',
      icon: Target,
      monthlyPrice: 9.99,
      yearlyPrice: 101.90,
      color: '#3b82f6',
      popular: false,
      description: 'Build your fitness foundation with intelligent AI guidance',
      features: [
        'Complete onboarding assessment with long-term memory storage',
        'Sleep Time Compute - AI works in background to optimize your next workout',
        'Smart scheduling with automatic adjustments when life gets in the way',
        'Equipment photo analysis - Snap photos for instant workout alternatives',
        'Fridge scanning for basic meal suggestions from available ingredients',
        'Unlimited personalized workout programs',
        'Basic form guidance and injury prevention tips',
        'Progress tracking with weekly analytics'
      ]
    },
    'performance_plan': {
      name: 'Performance Plan',
      icon: Zap,
      monthlyPrice: 19.99,
      yearlyPrice: 203.90,
      color: '#10b981',
      popular: true,
      description: 'Optimize your performance with advanced AI coaching',
      features: [
        'Everything from Foundation Plan',
        'Advanced Sleep Time Compute - Deeper analysis and optimization',
        'Smart meal planning with grocery list generation',
        'Advanced equipment alternatives with video demonstrations',
        'Comprehensive fridge scanning with recipe suggestions',
        'Advanced form analysis with real-time corrections',
        'Detailed progress analytics with trend analysis',
        'Nutrition coaching with macro tracking'
      ]
    },
    'champion_plan': {
      name: 'Champion Plan',
      icon: Crown,
      monthlyPrice: 29.99,
      yearlyPrice: 305.90,
      color: '#f59e0b',
      popular: false,
      description: 'Elite-level training with unlimited AI coaching',
      features: [
        'Everything from Performance Plan',
        'Predictive Sleep Time Compute - AI anticipates needs before you do',
        'Advanced behavioral learning - Milo becomes your perfect training partner',
        'Intelligent meal prep forecasting - Never run out of healthy options',
        'Proactive lifestyle integration - Seamless adaptation to your changing schedule',
        'Advanced biomechanics analysis with AI-powered form optimization',
        'Plateau prevention with intelligent program periodization',
        'Competition preparation and peak performance protocols'
      ]
    }
  }

  // Get current plan ID from customer data
  const getCurrentPlanId = () => {
    if (!currentProduct?.name) return null
    const productName = currentProduct.name.toLowerCase()
    if (productName.includes('foundation')) return 'foundation_plan'
    if (productName.includes('performance')) return 'performance_plan'
    if (productName.includes('champion')) return 'champion_plan'
    return null
  }

  const currentPlanId = getCurrentPlanId()

  // Helper functions for billing information
  const getBillingInfo = () => {
    if (!customer || !currentProduct) {
      return {
        nextBillingDate: 'No active subscription',
        hasPaymentMethod: false,
        planName: 'No Plan',
        monthlyTotal: '$0.00'
      }
    }

    // Mock billing data - in real app, get from Autumn customer data
    const nextBillingDate = new Date()
    nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)

    return {
      nextBillingDate: nextBillingDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      hasPaymentMethod: true, // Assume true if they have an active subscription
      planName: currentProduct.name || 'Unknown Plan',
      monthlyTotal: `$${(((currentProduct as any)?.price || 0) / 100).toFixed(2)}`
    }
  }

  const billingInfo = getBillingInfo()

  // Sidebar sections configuration
  const sidebarSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      subtitle: 'Tell us about your business details',
      icon: User,
      completed: true
    },
    {
      id: 'billing',
      title: 'Billing',
      subtitle: 'Information to confirm your identity',
      icon: CreditCard,
      completed: !!currentPlanId
    },
    {
      id: 'plans',
      title: 'Plans',
      subtitle: 'Provide your tax filing details',
      icon: Crown,
      completed: false
    },
    {
      id: 'usage',
      title: 'Usage',
      subtitle: 'Share how COVID-19 affected your business',
      icon: Zap,
      completed: false
    },
    {
      id: 'rewards',
      title: 'Rewards',
      subtitle: 'Upload necessary documents and details',
      icon: Target,
      completed: false
    }
  ]

  const handleSubscribe = async (planId: string) => {
    // Check if user already has this plan
    if (currentPlanId === planId) {
      return // Don't allow selecting current plan
    }

    setLoadingPlan(planId)

    try {
      console.log('Starting subscription for plan:', planId)
      console.log('User session:', session?.user)
      console.log('Autumn customer:', customer)

      // Use Autumn's attach function to redirect to Stripe checkout
      await attach({
        productId: planId,
        // Add billing cycle metadata if needed
        metadata: {
          billing_cycle: billingCycle,
          from_web: 'true'
        }
      })

      console.log('Attach function completed successfully')
    } catch (error) {
      console.error('Error starting subscription:', error)
      console.error('Error details:', {
        message: (error as any)?.message,
        stack: (error as any)?.stack,
        planId,
        billingCycle
      })
      // You could show a toast notification here
    } finally {
      setLoadingPlan(null)
    }
  }

  // Component to render content based on active section
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'basic-info':
        return (
          <Card className="bg-[#f5f2ea] dark:bg-[#0f0c05] dark:border-[#d6d1c4]/20 border-[#29261f]/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                Basic Information
              </CardTitle>
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.image || ''} alt={user.name || ''} />
                <AvatarFallback className="text-sm">{userInitials}</AvatarFallback>
              </Avatar>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex w-full justify-between">
                <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Name</p>
                <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.firstName} {user.lastName}</p>
              </div>
              <div className="flex w-full justify-between">
                <p className="font-manrope_1 text-sm font-bold text-black dark:text-white">Email</p>
                <p className="font-manrope_1 text-sm text-black/60 dark:text-white/60">{user.email}</p>
              </div>
            </CardContent>
          </Card>
        )

      case 'billing':
        return (
          <Card className="bg-[#f5f2ea] dark:bg-[#0f0c05] dark:border-[#d6d1c4]/20 border-[#29261f]/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                Billing
              </CardTitle>
              <Button
                variant="ghost"
                className="text-blue-600 hover:text-blue-700 font-manrope_1 text-sm"
                onClick={() => setIsPaymentHistoryOpen(true)}
              >
                Payment history
              </Button>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Trial Status */}
              {trialStatus.hasActivatedTrial && subscription?.status === 'trialing' && (
                <div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <Crown className="w-5 h-5 text-blue-600" />
                  <div>
                    <p className="text-lg font-semibold text-blue-900 dark:text-blue-100 font-manrope_1">
                      {getTrialDaysRemaining()} days left in trial
                    </p>
                    <p className="text-sm text-blue-700 dark:text-blue-300 font-manrope_1">
                      Trial ends on {subscription?.trialEnd ? new Date(subscription.trialEnd).toLocaleDateString() : 'N/A'}
                    </p>
                  </div>
                </div>
              )}

              {/* Next Billing Date */}
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                <div>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white font-manrope_1">
                    {billingInfo.nextBillingDate}
                  </p>
                  <p className="text-sm text-[#7e7b76] font-manrope_1">
                    {subscription?.status === 'trialing' ? 'Trial ends / Billing starts' : 'Next Billing Date'}
                  </p>
                </div>
              </div>

              {/* Payment Method */}
              {billingInfo.hasPaymentMethod && (
                <div className="flex items-center gap-3">
                  <CreditCard className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <div>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white font-manrope_1">
                      Payment method on file
                    </p>
                    <p className="text-sm text-[#7e7b76] font-manrope_1">
                      Managed through Stripe
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )

      case 'plans':
        return (
          <Card className="bg-[#f5f2ea] dark:bg-[#0f0c05] dark:border-[#d6d1c4]/20 border-[#29261f]/20">
            <CardHeader>
              <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
                Current plan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {currentPlanId ? (
                <>
                  <div className="border border-[#d6d1c4] dark:border-[#29261f] rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900 dark:text-white font-manrope_1">
                        {planConfigs[currentPlanId as keyof typeof planConfigs]?.name || billingInfo.planName}
                      </h3>
                      <Button
                        variant="ghost"
                        className="text-red-600 hover:text-red-700 font-manrope_1 text-sm"
                        onClick={() => setIsCancelDialogOpen(true)}
                      >
                        Cancel subscription
                      </Button>
                    </div>

                    <div className="space-y-2 mb-4">
                      {planConfigs[currentPlanId as keyof typeof planConfigs]?.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <Check size={16} color="#22c55e" className="mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300 font-manrope_1">
                            {feature}
                          </span>
                        </div>
                      ))}
                      <Button variant="ghost" className="text-blue-600 hover:text-blue-700 font-manrope_1 text-sm p-0 h-auto">
                        Show more
                      </Button>
                    </div>

                    <div className="space-y-1">
                      <p className="text-sm text-[#7e7b76] font-manrope_1">
                        {billingInfo.monthlyTotal}/user/mo • 1 seat purchased
                      </p>
                      <p className="font-semibold text-gray-900 dark:text-white font-manrope_1">
                        Monthly total: {billingInfo.monthlyTotal}
                      </p>
                    </div>
                  </div>

                  <div className="border border-[#d6d1c4] dark:border-[#29261f] rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white font-manrope_1 mb-1">
                          Manage subscription
                        </h4>
                        <p className="text-sm text-[#7e7b76] font-manrope_1">
                          To upgrade or downgrade your plan, please use the mobile app
                        </p>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-[#7e7b76] font-manrope_1 mb-4">No active subscription</p>
                  <p className="text-sm text-[#7e7b76] font-manrope_1">
                    To subscribe to a plan, please use the mobile app
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )

      case 'usage':
        return (
          <Card className="bg-[#f5f2ea] dark:bg-[#0f0c05] dark:border-[#d6d1c4]/20 border-[#29261f]/20">
            <CardHeader>
              <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Usage (Last 30 days)</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Premium models</span>
                      <Crown className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">{messagesUsed} / {messagesLimit}</div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.min((messagesUsed / messagesLimit) * 100, 100)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      You've used {messagesUsed} requests out of your {messagesLimit} fast requests quota.
                    </p>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Free models</span>
                      <Zap className="w-4 h-4 text-gray-400" />
                    </div>
                    <div className="text-lg font-semibold text-gray-900 dark:text-white">0 / 500</div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '0%' }}></div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      You've used no requests out of your 500 monthly fast requests quota.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )

      case 'rewards':
        return (
          <Card className="bg-[#f5f2ea] dark:bg-[#0f0c05] dark:border-[#d6d1c4]/20 border-[#29261f]/20">
            <CardHeader>
              <CardTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">Rewards</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <p className="text-[#7e7b76] font-manrope_1">Rewards system coming soon!</p>
              </div>
            </CardContent>
          </Card>
        )

      default:
        return null
    }
  }

  return (
    <div className="w-full min-h-screen bg-[#e9e5dc] dark:bg-[#1e1b16]">
      {isMobile ? (
        /* Mobile Layout */
        <>
          {/* Top Navigation */}
          <div className="fixed top-4 rounded-xl left-4 right-4 bg-[#2d4a3e] text-white border-b border-[#d6d1c4] dark:border-[#29261f] z-50">
            {/* Header */}
            <div className="flex items-center justify-between p-4">
              <h1 className="text-lg font-bold font-manrope_1 text-whitee">Settings</h1>
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="flex items-center gap-2 hover:bg-black/5 dark:hover:bg-white/5 rounded-lg p-2 transition-colors"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.image || ''} alt={user.name || ''} />
                    <AvatarFallback>{userInitials}</AvatarFallback>
                  </Avatar>
                  <ChevronDown className={`w-4 h-4 text-white/60 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                <AnimatePresence>
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                      className="absolute right-0 top-full mt-2 w-64 bg-[#e9e5dc] dark:bg-[#1e1b16] border border-[#d6d1c4] dark:border-[#29261f] rounded-lg shadow-lg overflow-hidden z-50"
                    >
                      <div className="p-4 border-b border-[#d6d1c4] dark:border-[#29261f]">
                        <div className="font-manrope_1 flex gap-3 items-center">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.image || ''} alt={user.name || ''} />
                            <AvatarFallback>{userInitials}</AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="text-black dark:text-white text-sm font-manrope_1 font-bold block">{user.name}</span>
                            <span className="text-[#7e7b76] text-xs font-manrope_1">{user.email}</span>
                          </div>
                        </div>
                      </div>
                      <div className="p-2">
                        <LogOutButton
                          variant="ghost"
                          className="w-full justify-start text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm font-manrope_1 hover:bg-black/5 dark:hover:bg-white/5"
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Mobile Navigation Tabs */}
            <div className="flex overflow-x-auto px-4 pb-2">
              {sidebarSections.map((section) => {
                const isActive = activeSection === section.id
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`flex-shrink-0 px-4 py-2 mr-2 rounded-lg text-sm font-manrope_1 transition-colors ${
                      isActive
                        ? 'bg-white/5 text-white'
                        : 'text-[#7e7b76] hover:bg-black/5 dark:hover:bg-white/5'
                    }`}
                  >
                    {section.title}
                  </button>
                )
              })}
            </div>
          </div>

          {/* Mobile Content */}
          <div className="pt-40 pb-4 px-4">
            <div className="max-w-7xl mx-auto">
              {renderSectionContent()}
            </div>
          </div>
        </>
      ) : (
        /* Desktop Layout with Sidebar */
        <div className="flex min-h-screen pl-[2rem] py-[2rem]">
          {/* Sidebar */}
          <div className="w-80 bg-[#2d4a3e] rounded-xl text-white p-6 flex flex-col">
            {/* Header */}
            <div className="mb-8">
            
              <h1 className="text-xl font-bold font-manrope_1">Settings</h1>
            </div>

            {/* Navigation */}
            <nav className="flex-1 space-y-2">
              {sidebarSections.map((section) => {
                const isActive = activeSection === section.id

                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors flex items-center gap-3 ${
                      isActive
                        ? 'bg-white/10 text-white'
                        : 'text-white/70 hover:bg-white/5 hover:text-white'
                    }`}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        section.completed
                          ? 'bg-green-500 border-green-500'
                          : isActive
                            ? 'border-white'
                            : 'border-white/30'
                      }`}>
                        {section.completed && <Check size={14} />}
                      </div>
                      <div>
                        <div className="font-medium font-manrope_1">{section.title}</div>
                        <div className="text-xs text-white/60 font-manrope_1">{section.subtitle}</div>
                      </div>
                    </div>
                  </button>
                )
              })}
            </nav>

            {/* Bottom User Section */}
            <div className="border-t border-white/20 pt-4">
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors"
                >
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.image || ''} alt={user.name || ''} />
                    <AvatarFallback>{userInitials}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 text-left">
                    <div className="font-medium font-manrope_1">{user.name}</div>
                    <div className="text-xs text-white/60 font-manrope_1">{user.email}</div>
                  </div>
                  <ChevronDown className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
                </button>

                <AnimatePresence>
                  {isDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                      className="absolute bottom-full left-0 right-0 mb-2 bg-[#e9e5dc] dark:bg-[#1e1b16] border border-[#d6d1c4] dark:border-[#29261f] rounded-lg shadow-lg overflow-hidden"
                    >
                      <div className="p-2">
                        <LogOutButton
                          variant="ghost"
                          className="w-full justify-start text-[#7e7b76] hover:text-gray-800 dark:hover:text-gray-200 text-sm font-manrope_1 hover:bg-black/5 dark:hover:bg-white/5"
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 ">
            <div className="max-w-4xl mx-auto">
              {renderSectionContent()}
            </div>
          </div>
        </div>
      )}

    

      {/* Cancel Subscription Dialog */}
      <AnimatePresence>
        {isCancelDialogOpen && (
          <Dialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.95 }}
              transition={{ duration: 0, ease: "easeOut" }}
            >
          <DialogContent className="max-w-md bg-[#f5f2ea] dark:bg-[#0f0c05]">
          <DialogHeader>
            <DialogTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
              Cancel your subscription?
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            <p className="text-sm text-[#7e7b76] font-manrope_1">
              If you cancel your subscription:
            </p>
            <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300 font-manrope_1">
              <li>• You will lose access to premium features</li>
              <li>• Your subscription will remain active until the end of your billing period</li>
              <li>• You won't be charged again after your current billing period</li>
            </ul>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsCancelDialogOpen(false)}
              className="font-manrope_1"
            >
              Cancel
            </Button>
            <Button
              className="bg-red-600 hover:bg-red-700 text-white font-manrope_1"
              onClick={() => {
                // Handle cancellation logic here
                setIsCancelDialogOpen(false)
              }}
            >
              Cancel subscription
            </Button>
          </div>
          </DialogContent>
            </motion.div>
          </Dialog>
        )}
      </AnimatePresence>

      {/* Payment History Dialog */}
      <AnimatePresence>
        {isPaymentHistoryOpen && (
          <Dialog open={isPaymentHistoryOpen} onOpenChange={setIsPaymentHistoryOpen}>
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 20, scale: 0.95 }}
              transition={{ duration: 0, ease: "easeOut" }}
            >
          <DialogContent className="max-w-2xl bg-[#f5f2ea] dark:bg-[#0f0c05]">
          <DialogHeader>
            <DialogTitle className="text-lg font-manrope_1 font-bold text-gray-900 dark:text-white">
              Payment History
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 mt-4">
            <div className="text-center py-8">
              <p className="text-[#7e7b76] font-manrope_1">No payment history available</p>
              <p className="text-sm text-[#7e7b76] font-manrope_1 mt-2">
                Your payment history will appear here once you have an active subscription.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <Button
              variant="outline"
              onClick={() => setIsPaymentHistoryOpen(false)}
              className="font-manrope_1"
            >
              Close
            </Button>
          </div>
          </DialogContent>
            </motion.div>
          </Dialog>
        )}
      </AnimatePresence>
    </div>
  )
}